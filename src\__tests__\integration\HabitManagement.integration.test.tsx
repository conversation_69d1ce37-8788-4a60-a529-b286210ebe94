/**
 * Habit Management Integration Tests
 * Tests for complete habit management workflows and user interactions
 */

import React from 'react';
import { fireEvent, waitFor } from '@testing-library/react-native';
import { render, createMockHabit, createMockUser, waitForLoadingToFinish } from '../../test/utils';
import HabitsScreen from '../../../app/(tabs)/habits';
import HabitFormScreen from '../../../app/habit-form';
import HabitDetailScreen from '../../../app/habit-detail';

// Mock the useHabits hook
const mockUseHabits = {
  activeHabits: [],
  isLoading: false,
  createHabit: jest.fn(),
  updateHabit: jest.fn(),
  deleteHabit: jest.fn(),
  deleteHabits: jest.fn(),
  archiveHabits: jest.fn(),
  reorderHabits: jest.fn(),
  toggleHabitCompletion: jest.fn(),
  getHabitById: jest.fn(),
};

jest.mock('../../hooks/useHabits', () => ({
  useHabits: () => mockUseHabits,
}));

describe('Habit Management Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseHabits.activeHabits = [];
    mockUseHabits.isLoading = false;
  });

  describe('Habit Creation Flow', () => {
    it('should complete the full habit creation workflow', async () => {
      const mockRouter = {
        push: jest.fn(),
        back: jest.fn(),
      };

      // Mock successful habit creation
      mockUseHabits.createHabit.mockResolvedValue({
        id: 'new-habit-id',
        name: 'New Test Habit',
      });

      // Render habit form screen
      const { getByTestId, getByText } = render(<HabitFormScreen />);

      // Fill out the form
      const nameInput = getByTestId('habit-name-input');
      const descriptionInput = getByTestId('habit-description-input');
      const submitButton = getByText('Create Habit');

      fireEvent.changeText(nameInput, 'New Test Habit');
      fireEvent.changeText(descriptionInput, 'A new habit for testing');

      // Submit the form
      fireEvent.press(submitButton);

      // Wait for the creation to complete
      await waitFor(() => {
        expect(mockUseHabits.createHabit).toHaveBeenCalledWith(
          expect.objectContaining({
            name: 'New Test Habit',
            description: 'A new habit for testing',
          })
        );
      });

      // Verify navigation back to habits screen
      expect(mockRouter.back).toHaveBeenCalled();
    });

    it('should handle form validation errors', async () => {
      const { getByTestId, getByText } = render(<HabitFormScreen />);

      const submitButton = getByText('Create Habit');

      // Try to submit without filling required fields
      fireEvent.press(submitButton);

      // Should show validation errors
      await waitFor(() => {
        expect(getByText('Habit name is required')).toBeTruthy();
      });

      // Should not call createHabit
      expect(mockUseHabits.createHabit).not.toHaveBeenCalled();
    });

    it('should handle creation errors gracefully', async () => {
      mockUseHabits.createHabit.mockRejectedValue(new Error('Creation failed'));

      const { getByTestId, getByText } = render(<HabitFormScreen />);

      const nameInput = getByTestId('habit-name-input');
      const submitButton = getByText('Create Habit');

      fireEvent.changeText(nameInput, 'Test Habit');
      fireEvent.press(submitButton);

      await waitFor(() => {
        expect(getByText('Failed to create habit')).toBeTruthy();
      });
    });
  });

  describe('Habit List and Interaction Flow', () => {
    it('should display habits and handle completion toggle', async () => {
      const mockHabits = [
        createMockHabit({ id: '1', name: 'Morning Exercise' }),
        createMockHabit({ id: '2', name: 'Read Books' }),
      ];

      mockUseHabits.activeHabits = mockHabits;

      const { getByText, getAllByTestId } = render(<HabitsScreen />);

      // Verify habits are displayed
      expect(getByText('Morning Exercise')).toBeTruthy();
      expect(getByText('Read Books')).toBeTruthy();

      // Find and press completion button for first habit
      const completionButtons = getAllByTestId('completion-button');
      fireEvent.press(completionButtons[0]);

      // Verify completion toggle was called
      expect(mockUseHabits.toggleHabitCompletion).toHaveBeenCalledWith(mockHabits[0]);
    });

    it('should handle empty state correctly', () => {
      mockUseHabits.activeHabits = [];

      const { getByText } = render(<HabitsScreen />);

      expect(getByText('No habits yet')).toBeTruthy();
      expect(getByText('Start building your routine by creating your first habit')).toBeTruthy();
    });

    it('should show loading state', () => {
      mockUseHabits.isLoading = true;

      const { getByTestId } = render(<HabitsScreen />);

      expect(getByTestId('loading-indicator')).toBeTruthy();
    });
  });

  describe('Habit Detail Navigation Flow', () => {
    it('should navigate to habit detail and display information', async () => {
      const mockHabit = createMockHabit({
        id: 'test-habit-id',
        name: 'Test Habit',
        description: 'Test Description',
      });

      mockUseHabits.getHabitById.mockReturnValue(mockHabit);

      // Mock route params
      jest.mock('expo-router', () => ({
        ...jest.requireActual('expo-router'),
        useLocalSearchParams: () => ({ habitId: 'test-habit-id' }),
      }));

      const { getByText } = render(<HabitDetailScreen />);

      expect(getByText('Test Habit')).toBeTruthy();
      expect(getByText('Test Description')).toBeTruthy();
    });

    it('should handle habit not found scenario', () => {
      mockUseHabits.getHabitById.mockReturnValue(null);

      const { getByText } = render(<HabitDetailScreen />);

      expect(getByText('Habit not found')).toBeTruthy();
      expect(getByText('Go Back')).toBeTruthy();
    });
  });

  describe('Bulk Operations Flow', () => {
    it('should handle bulk deletion workflow', async () => {
      const mockHabits = [
        createMockHabit({ id: '1', name: 'Habit 1' }),
        createMockHabit({ id: '2', name: 'Habit 2' }),
        createMockHabit({ id: '3', name: 'Habit 3' }),
      ];

      mockUseHabits.activeHabits = mockHabits;
      mockUseHabits.deleteHabits.mockResolvedValue(undefined);

      const { getByText, getAllByTestId, getByTestId } = render(<HabitsScreen />);

      // Enter selection mode by long pressing a habit
      const habitCards = getAllByTestId('habit-card');
      fireEvent(habitCards[0], 'onLongPress');

      // Verify selection mode is active
      await waitFor(() => {
        expect(getByTestId('bulk-actions-container')).toBeTruthy();
      });

      // Select multiple habits
      fireEvent.press(habitCards[1]);
      fireEvent.press(habitCards[2]);

      // Perform bulk delete
      const deleteButton = getByText('Delete');
      fireEvent.press(deleteButton);

      // Confirm deletion
      const confirmButton = getByText('Delete Habits');
      fireEvent.press(confirmButton);

      // Verify bulk delete was called
      await waitFor(() => {
        expect(mockUseHabits.deleteHabits).toHaveBeenCalledWith(['1', '2', '3']);
      });
    });

    it('should handle bulk archive workflow', async () => {
      const mockHabits = [
        createMockHabit({ id: '1', name: 'Habit 1' }),
        createMockHabit({ id: '2', name: 'Habit 2' }),
      ];

      mockUseHabits.activeHabits = mockHabits;
      mockUseHabits.archiveHabits.mockResolvedValue(undefined);

      const { getByText, getAllByTestId, getByTestId } = render(<HabitsScreen />);

      // Enter selection mode
      const habitCards = getAllByTestId('habit-card');
      fireEvent(habitCards[0], 'onLongPress');

      // Select habits
      fireEvent.press(habitCards[1]);

      // Perform bulk archive
      const archiveButton = getByText('Archive');
      fireEvent.press(archiveButton);

      // Confirm archiving
      const confirmButton = getByText('Archive Habits');
      fireEvent.press(confirmButton);

      // Verify bulk archive was called
      await waitFor(() => {
        expect(mockUseHabits.archiveHabits).toHaveBeenCalledWith(['1', '2']);
      });
    });
  });

  describe('Habit Reordering Flow', () => {
    it('should handle habit reordering workflow', async () => {
      const mockHabits = [
        createMockHabit({ id: '1', name: 'Habit 1', order_index: 0 }),
        createMockHabit({ id: '2', name: 'Habit 2', order_index: 1 }),
        createMockHabit({ id: '3', name: 'Habit 3', order_index: 2 }),
      ];

      mockUseHabits.activeHabits = mockHabits;
      mockUseHabits.reorderHabits.mockResolvedValue(undefined);

      const { getByText, getByTestId } = render(<HabitsScreen />);

      // Open reorder modal
      const reorderButton = getByText('Reorder');
      fireEvent.press(reorderButton);

      // Verify reorder modal is open
      await waitFor(() => {
        expect(getByText('Reorder Habits')).toBeTruthy();
      });

      // Simulate reordering (move first habit down)
      const moveDownButton = getByTestId('move-down-0');
      fireEvent.press(moveDownButton);

      // Save the new order
      const saveButton = getByText('Save');
      fireEvent.press(saveButton);

      // Verify reorder was called
      await waitFor(() => {
        expect(mockUseHabits.reorderHabits).toHaveBeenCalled();
      });
    });
  });

  describe('Error Handling Integration', () => {
    it('should handle network errors gracefully', async () => {
      mockUseHabits.toggleHabitCompletion.mockRejectedValue(new Error('Network error'));

      const mockHabits = [createMockHabit({ id: '1', name: 'Test Habit' })];
      mockUseHabits.activeHabits = mockHabits;

      const { getByTestId, getByText } = render(<HabitsScreen />);

      const completionButton = getByTestId('completion-button');
      fireEvent.press(completionButton);

      await waitFor(() => {
        expect(getByText('Failed to update habit')).toBeTruthy();
      });
    });

    it('should handle database errors during operations', async () => {
      mockUseHabits.deleteHabits.mockRejectedValue(new Error('Database error'));

      const mockHabits = [createMockHabit({ id: '1', name: 'Test Habit' })];
      mockUseHabits.activeHabits = mockHabits;

      const { getAllByTestId, getByText } = render(<HabitsScreen />);

      // Enter selection mode and select habit
      const habitCards = getAllByTestId('habit-card');
      fireEvent(habitCards[0], 'onLongPress');

      // Try to delete
      const deleteButton = getByText('Delete');
      fireEvent.press(deleteButton);

      const confirmButton = getByText('Delete Habits');
      fireEvent.press(confirmButton);

      await waitFor(() => {
        expect(getByText('Failed to delete habits')).toBeTruthy();
      });
    });
  });
});
