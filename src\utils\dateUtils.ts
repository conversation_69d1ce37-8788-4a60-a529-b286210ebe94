/**
 * Date utility functions for the WinArc app
 */

export const formatDate = (date: Date | string): string => {
  const d = typeof date === 'string' ? new Date(date) : date;
  return d.toISOString().split('T')[0];
};

export const formatDisplayDate = (date: Date | string): string => {
  const d = typeof date === 'string' ? new Date(date) : date;
  return d.toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
};

export const formatShortDate = (date: Date | string): string => {
  const d = typeof date === 'string' ? new Date(date) : date;
  return d.toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
  });
};

export const getToday = (): string => {
  return formatDate(new Date());
};

export const getYesterday = (): string => {
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  return formatDate(yesterday);
};

export const getTomorrow = (): string => {
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1);
  return formatDate(tomorrow);
};

export const getWeekStart = (date?: Date | string): string => {
  const d = date ? (typeof date === 'string' ? new Date(date) : date) : new Date();
  const day = d.getDay();
  const diff = d.getDate() - day;
  const weekStart = new Date(d.setDate(diff));
  return formatDate(weekStart);
};

export const getWeekEnd = (date?: Date | string): string => {
  const d = date ? (typeof date === 'string' ? new Date(date) : date) : new Date();
  const day = d.getDay();
  const diff = d.getDate() - day + 6;
  const weekEnd = new Date(d.setDate(diff));
  return formatDate(weekEnd);
};

export const getMonthStart = (date?: Date | string): string => {
  const d = date ? (typeof date === 'string' ? new Date(date) : date) : new Date();
  const monthStart = new Date(d.getFullYear(), d.getMonth(), 1);
  return formatDate(monthStart);
};

export const getMonthEnd = (date?: Date | string): string => {
  const d = date ? (typeof date === 'string' ? new Date(date) : date) : new Date();
  const monthEnd = new Date(d.getFullYear(), d.getMonth() + 1, 0);
  return formatDate(monthEnd);
};

export const getDaysInMonth = (date?: Date | string): number => {
  const d = date ? (typeof date === 'string' ? new Date(date) : date) : new Date();
  return new Date(d.getFullYear(), d.getMonth() + 1, 0).getDate();
};

export const getDaysBetween = (startDate: Date | string, endDate: Date | string): number => {
  const start = typeof startDate === 'string' ? new Date(startDate) : startDate;
  const end = typeof endDate === 'string' ? new Date(endDate) : endDate;
  const diffTime = Math.abs(end.getTime() - start.getTime());
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};

export const isToday = (date: Date | string): boolean => {
  const d = typeof date === 'string' ? new Date(date) : date;
  const today = new Date();
  return d.toDateString() === today.toDateString();
};

export const isYesterday = (date: Date | string): boolean => {
  const d = typeof date === 'string' ? new Date(date) : date;
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  return d.toDateString() === yesterday.toDateString();
};

export const isSameWeek = (date1: Date | string, date2: Date | string): boolean => {
  const d1 = typeof date1 === 'string' ? new Date(date1) : date1;
  const d2 = typeof date2 === 'string' ? new Date(date2) : date2;
  
  const weekStart1 = getWeekStart(d1);
  const weekStart2 = getWeekStart(d2);
  
  return weekStart1 === weekStart2;
};

export const isSameMonth = (date1: Date | string, date2: Date | string): boolean => {
  const d1 = typeof date1 === 'string' ? new Date(date1) : date1;
  const d2 = typeof date2 === 'string' ? new Date(date2) : date2;
  
  return d1.getFullYear() === d2.getFullYear() && d1.getMonth() === d2.getMonth();
};

export const getRelativeTimeString = (date: Date | string): string => {
  const d = typeof date === 'string' ? new Date(date) : date;
  const now = new Date();
  const diffMs = now.getTime() - d.getTime();
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  
  if (diffDays === 0) return 'Today';
  if (diffDays === 1) return 'Yesterday';
  if (diffDays < 7) return `${diffDays} days ago`;
  if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;
  if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`;
  return `${Math.floor(diffDays / 365)} years ago`;
};
