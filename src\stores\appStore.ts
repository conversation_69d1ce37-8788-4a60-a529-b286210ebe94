import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

export type Theme = 'light' | 'dark' | 'system';
export type Language = 'en' | 'es' | 'fr' | 'de' | 'zh' | 'ja';

interface AppState {
  // App settings
  theme: Theme;
  language: Language;
  isFirstLaunch: boolean;
  hasCompletedOnboarding: boolean;

  // UI state
  isLoading: boolean;
  activeTab: string;

  // Notifications
  notificationsEnabled: boolean;
  reminderNotificationsEnabled: boolean;

  // Timer settings
  pomodoroWorkDuration: number; // in minutes
  pomodoroBreakDuration: number; // in minutes
  pomodoroLongBreakDuration: number; // in minutes
  pomodoroSessionsUntilLongBreak: number;

  // Actions
  setTheme: (theme: Theme) => void;
  setLanguage: (language: Language) => void;
  setFirstLaunch: (isFirstLaunch: boolean) => void;
  setOnboardingCompleted: (completed: boolean) => void;
  setLoading: (isLoading: boolean) => void;
  setActiveTab: (tab: string) => void;
  setNotificationsEnabled: (enabled: boolean) => void;
  setReminderNotificationsEnabled: (enabled: boolean) => void;
  setPomodoroSettings: (settings: {
    workDuration?: number;
    breakDuration?: number;
    longBreakDuration?: number;
    sessionsUntilLongBreak?: number;
  }) => void;
  resetApp: () => void;
}

const initialState = {
  theme: 'system' as Theme,
  language: 'en' as Language,
  isFirstLaunch: true,
  hasCompletedOnboarding: false,
  isLoading: false,
  activeTab: 'home',
  notificationsEnabled: true,
  reminderNotificationsEnabled: true,
  pomodoroWorkDuration: 25,
  pomodoroBreakDuration: 5,
  pomodoroLongBreakDuration: 15,
  pomodoroSessionsUntilLongBreak: 4,
};

export const useAppStore = create<AppState>()(
  devtools(
    persist(
      (set, get) => ({
        ...initialState,

        setTheme: theme => {
          set({ theme }, false, 'setTheme');
        },

        setLanguage: language => {
          set({ language }, false, 'setLanguage');
        },

        setFirstLaunch: isFirstLaunch => {
          set({ isFirstLaunch }, false, 'setFirstLaunch');
        },

        setOnboardingCompleted: completed => {
          set({ hasCompletedOnboarding: completed }, false, 'setOnboardingCompleted');
        },

        setLoading: isLoading => {
          set({ isLoading }, false, 'setLoading');
        },

        setActiveTab: tab => {
          set({ activeTab: tab }, false, 'setActiveTab');
        },

        setNotificationsEnabled: enabled => {
          set({ notificationsEnabled: enabled }, false, 'setNotificationsEnabled');
        },

        setReminderNotificationsEnabled: enabled => {
          set({ reminderNotificationsEnabled: enabled }, false, 'setReminderNotificationsEnabled');
        },

        setPomodoroSettings: settings => {
          const currentState = get();
          set(
            {
              pomodoroWorkDuration: settings.workDuration ?? currentState.pomodoroWorkDuration,
              pomodoroBreakDuration: settings.breakDuration ?? currentState.pomodoroBreakDuration,
              pomodoroLongBreakDuration:
                settings.longBreakDuration ?? currentState.pomodoroLongBreakDuration,
              pomodoroSessionsUntilLongBreak:
                settings.sessionsUntilLongBreak ?? currentState.pomodoroSessionsUntilLongBreak,
            },
            false,
            'setPomodoroSettings'
          );
        },

        resetApp: () => {
          set(initialState, false, 'resetApp');
        },
      }),
      {
        name: 'app-store',
        storage: (() => {
          // Import AsyncStorage and createJSONStorage locally
          const AsyncStorage = require('@react-native-async-storage/async-storage').default;
          const { createJSONStorage } = require('zustand/middleware');
          return createJSONStorage(() => AsyncStorage);
        })(),
        partialize: state => ({
          theme: state.theme,
          language: state.language,
          isFirstLaunch: state.isFirstLaunch,
          hasCompletedOnboarding: state.hasCompletedOnboarding,
          notificationsEnabled: state.notificationsEnabled,
          reminderNotificationsEnabled: state.reminderNotificationsEnabled,
          pomodoroWorkDuration: state.pomodoroWorkDuration,
          pomodoroBreakDuration: state.pomodoroBreakDuration,
          pomodoroLongBreakDuration: state.pomodoroLongBreakDuration,
          pomodoroSessionsUntilLongBreak: state.pomodoroSessionsUntilLongBreak,
        }),
      }
    ),
    {
      name: 'app-store',
    }
  )
);
