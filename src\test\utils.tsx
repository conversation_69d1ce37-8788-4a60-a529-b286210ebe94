/**
 * Test Utilities
 * Common utilities and helpers for testing WinArc components
 */

import { render, RenderOptions } from '@testing-library/react-native';
import React from 'react';
import { Habit } from '../database/entities/Habit';
import { HabitCompletion } from '../database/entities/HabitCompletion';
import { User } from '../database/entities/User';

// Mock Theme Provider for testing
const MockThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return <>{children}</>;
};

// Custom render function with providers
const customRender = (ui: React.ReactElement, options?: RenderOptions) => {
  const Wrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
    <MockThemeProvider>{children}</MockThemeProvider>
  );

  return render(ui, { wrapper: Wrapper, ...options });
};

// Re-export everything from React Native Testing Library
export * from '@testing-library/react-native';
export { customRender as render };

// Mock data generators
export const createMockUser = (overrides?: Partial<User>): User => ({
  id: 'test-user-id',
  name: 'Test User',
  email: '<EMAIL>',
  created_at: new Date(),
  updated_at: new Date(),
  habits: [],
  ...overrides,
});

export const createMockHabit = (overrides?: Partial<Habit>): Habit => ({
  id: 'test-habit-id',
  name: 'Test Habit',
  description: 'A test habit for testing purposes',
  category: 'health',
  frequency: 'daily',
  target_count: 1,
  color: '#3B82F6',
  reminder_time: null,
  is_active: true,
  created_at: new Date(),
  updated_at: new Date(),
  user_id: 'test-user-id',
  user: createMockUser(),
  completions: [],
  streaks: [],
  order_index: 0,
  ...overrides,
});

export const createMockHabitCompletion = (overrides?: Partial<HabitCompletion>): HabitCompletion => ({
  id: 'test-completion-id',
  habit_id: 'test-habit-id',
  completed_date: new Date().toISOString().split('T')[0],
  count: 1,
  notes: null,
  created_at: new Date(),
  habit: createMockHabit(),
  ...overrides,
});

// Mock habit statistics
export const createMockHabitStats = () => ({
  currentStreak: 5,
  longestStreak: 10,
  totalCompletions: 25,
  completionRate: 0.8,
  averageCompletionsPerWeek: 6,
  averageCompletionsPerMonth: 24,
  lastCompletedDate: new Date(),
});

// Mock progress data
export const createMockProgressData = (days: number = 30) => {
  const data = [];
  const today = new Date();

  for (let i = days - 1; i >= 0; i--) {
    const date = new Date(today);
    date.setDate(date.getDate() - i);

    data.push({
      date: date.toISOString().split('T')[0],
      completed: Math.random() > 0.3, // 70% completion rate
      count: Math.random() > 0.3 ? 1 : 0,
    });
  }

  return data;
};

// Test helpers
export const waitForLoadingToFinish = async () => {
  // Wait for any loading states to complete
  await new Promise(resolve => setTimeout(resolve, 100));
};

export const mockAsyncStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};

// Mock navigation helpers
export const createMockRouter = () => ({
  push: jest.fn(),
  back: jest.fn(),
  replace: jest.fn(),
  canGoBack: jest.fn(() => true),
});

// Mock form data
export const createMockHabitFormData = () => ({
  name: 'Test Habit',
  description: 'A test habit description',
  category: 'health' as const,
  frequency: 'daily' as const,
  target_count: 1,
  color: '#3B82F6',
  reminder_time: null,
});

// Test assertions helpers
export const expectHabitCardToBeRendered = (habitName: string) => {
  expect(screen.getByText(habitName)).toBeTruthy();
};

export const expectLoadingStateToBeShown = () => {
  expect(screen.getByTestId('loading-indicator')).toBeTruthy();
};

export const expectEmptyStateToBeShown = (title: string) => {
  expect(screen.getByText(title)).toBeTruthy();
};

// Mock gesture handlers for drag and drop testing
export const mockGestureHandler = {
  onGestureEvent: jest.fn(),
  onHandlerStateChange: jest.fn(),
};

// Mock theme values
export const mockTheme = {
  light: {
    background: '#FFFFFF',
    text: '#000000',
    primary: '#3B82F6',
  },
  dark: {
    background: '#1F2937',
    text: '#FFFFFF',
    primary: '#60A5FA',
  },
};

// Performance testing helpers
export const measureRenderTime = async (renderFn: () => void) => {
  const start = performance.now();
  renderFn();
  const end = performance.now();
  return end - start;
};

// Accessibility testing helpers
export const checkAccessibilityProps = (element: any) => {
  expect(element).toHaveAccessibilityRole();
  expect(element).toHaveAccessibilityLabel();
};

// Database mock helpers
export const createMockRepository = <T,>() => ({
  find: jest.fn(),
  findOne: jest.fn(),
  save: jest.fn(),
  remove: jest.fn(),
  create: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
  count: jest.fn(),
});

// Error simulation helpers
export const simulateNetworkError = () => {
  throw new Error('Network request failed');
};

export const simulateDatabaseError = () => {
  throw new Error('Database operation failed');
};

// Date helpers for testing
export const createDateString = (daysAgo: number = 0): string => {
  const date = new Date();
  date.setDate(date.getDate() - daysAgo);
  return date.toISOString().split('T')[0];
};

export const createTimeString = (hours: number, minutes: number): string => {
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
};
