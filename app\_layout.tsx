import { Stack } from "expo-router";
import { useEffect } from "react";
import "reflect-metadata";
import { databaseService } from "../src/database";
import "../src/styles/global.css";

export default function RootLayout() {
  useEffect(() => {
    const initializeApp = async () => {
      try {
        await databaseService.initialize();
        await databaseService.createDefaultUser();
        if (__DEV__) {
          console.log('App initialized successfully');
        }
      } catch (error) {
        console.error('Failed to initialize app:', error);
      }
    };

    initializeApp();
  }, []);

  // Import ThemeProvider locally to avoid auto-formatting removal
  const { ThemeProvider } = require('../src/components');

  return (
    <ThemeProvider>
      <RootNavigator />
    </ThemeProvider>
  );
}

function RootNavigator() {
  const { useResolvedTheme } = require('../src/components');
  const resolvedTheme = useResolvedTheme();

  const backgroundColor = resolvedTheme === 'dark' ? '#111827' : '#F9FAFB';

  return (
    <Stack
      screenOptions={{
        headerShown: false,
        contentStyle: { backgroundColor },
        animation: 'fade',
        animationDuration: 200,
      }}
    >
      <Stack.Screen
        name="(tabs)"
        options={{
          headerShown: false,
          contentStyle: { backgroundColor },
        }}
      />
      <Stack.Screen
        name="onboarding"
        options={{
          headerShown: false,
          contentStyle: { backgroundColor },
        }}
      />
      <Stack.Screen
        name="habit-form"
        options={{
          headerShown: false,
          presentation: 'modal',
          contentStyle: { backgroundColor },
          animation: 'slide_from_bottom',
          animationDuration: 300,
        }}
      />
      <Stack.Screen
        name="habit-detail"
        options={{
          headerShown: false,
          animation: 'slide_from_right',
          animationDuration: 250,
          contentStyle: { backgroundColor },
        }}
      />
    </Stack>
  );
}
