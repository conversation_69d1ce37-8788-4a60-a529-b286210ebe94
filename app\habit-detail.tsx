import { Ionicons } from '@expo/vector-icons';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useState } from 'react';
import {
  ActionSheetIOS,
  Alert,
  Platform,
  ScrollView,
  Text,
  TouchableOpacity,
  View
} from 'react-native';
import { HabitProgressChart, HabitStatsCard } from '../src/components/habits';
import { useHabits } from '../src/hooks/useHabits';
import { habitStatisticsService } from '../src/services/habitStatisticsService';
import { HabitStats } from '../src/types/habit.types';

export default function HabitDetailScreen() {
  const router = useRouter();
  const { habitId } = useLocalSearchParams<{ habitId: string }>();
  const {
    getHabitById,
    deleteHabit,
    toggleHabitCompletion,
    getCurrentStreak,
    getLongestStreak,
    isHabitCompletedToday,
    getTodayCompletionCount
  } = useHabits();

  const habit = getHabitById(habitId);
  const [isDeleting, setIsDeleting] = useState(false);
  const [stats, setStats] = useState<HabitStats | null>(null);
  const [progressData, setProgressData] = useState<Array<{ date: string; completed: boolean; count: number }>>([]);
  const [isLoadingStats, setIsLoadingStats] = useState(true);

  // Load habit statistics
  React.useEffect(() => {
    if (habit) {
      loadHabitStatistics();
    }
  }, [habit]);

  const loadHabitStatistics = async () => {
    if (!habit) return;

    setIsLoadingStats(true);
    try {
      const [habitStats, progressHistory] = await Promise.all([
        habitStatisticsService.calculateHabitStats(habit.id),
        habitStatisticsService.getCompletionHistory(habit.id, 30)
      ]);

      setStats(habitStats);
      setProgressData(progressHistory);
    } catch (error) {
      console.error('Failed to load habit statistics:', error);
    } finally {
      setIsLoadingStats(false);
    }
  };

  if (!habit) {
    return (
      <View className="flex-1 bg-gray-50 dark:bg-gray-900 items-center justify-center">
        <Ionicons name="alert-circle-outline" size={64} color="#9CA3AF" />
        <Text className="text-xl font-semibold text-gray-900 dark:text-gray-100 mt-4">
          Habit not found
        </Text>
        <TouchableOpacity
          onPress={() => router.back()}
          className="bg-blue-500 px-6 py-3 rounded-lg mt-6"
        >
          <Text className="text-white font-semibold">Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  const isCompletedToday = isHabitCompletedToday(habitId);
  const todayCount = getTodayCompletionCount(habitId);
  const currentStreak = getCurrentStreak(habitId);
  const longestStreak = getLongestStreak(habitId);

  const handleEdit = () => {
    router.push(`/habit-form?habitId=${habitId}`);
  };

  const handleDelete = async () => {
    const showActionSheet = () => {
      if (Platform.OS === 'ios') {
        ActionSheetIOS.showActionSheetWithOptions(
          {
            options: ['Cancel', 'Delete Habit'],
            destructiveButtonIndex: 1,
            cancelButtonIndex: 0,
            title: 'Are you sure you want to delete this habit?',
            message: 'This action cannot be undone.',
          },
          (buttonIndex) => {
            if (buttonIndex === 1) {
              confirmDelete();
            }
          }
        );
      } else {
        Alert.alert(
          'Delete Habit',
          'Are you sure you want to delete this habit? This action cannot be undone.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Delete', style: 'destructive', onPress: confirmDelete },
          ]
        );
      }
    };

    const confirmDelete = async () => {
      setIsDeleting(true);
      try {
        await deleteHabit(habitId);
        Alert.alert(
          'Success',
          'Habit deleted successfully',
          [{ text: 'OK', onPress: () => router.back() }]
        );
      } catch (error) {
        Alert.alert(
          'Error',
          error instanceof Error ? error.message : 'Failed to delete habit'
        );
      } finally {
        setIsDeleting(false);
      }
    };

    showActionSheet();
  };

  const handleToggleCompletion = async () => {
    try {
      await toggleHabitCompletion(habitId);
    } catch (error) {
      Alert.alert(
        'Error',
        error instanceof Error ? error.message : 'Failed to update habit'
      );
    }
  };

  return (
    <View className="flex-1 bg-gray-50 dark:bg-gray-900">
      <StatusBar style="auto" />

      {/* Header */}
      <View className="bg-white dark:bg-gray-800 px-6 pt-12 pb-6 shadow-sm">
        <View className="flex-row items-center justify-between">
          <TouchableOpacity onPress={() => router.back()}>
            <Ionicons name="arrow-back" size={24} color="#6B7280" />
          </TouchableOpacity>
          <Text className="text-xl font-bold text-gray-900 dark:text-gray-100">
            Habit Details
          </Text>
          <TouchableOpacity onPress={handleEdit}>
            <Ionicons name="create-outline" size={24} color="#6B7280" />
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView className="flex-1 px-6 pt-6">
        {/* Habit Info Card */}
        <View className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm mb-6">
          <View className="flex-row items-start justify-between mb-4">
            <View className="flex-1 mr-4">
              <View className="flex-row items-center mb-2">
                <View
                  className="w-6 h-6 rounded-full mr-3"
                  style={{ backgroundColor: habit.color }}
                />
                <Text className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {habit.name}
                </Text>
              </View>

              {habit.description && (
                <Text className="text-gray-600 dark:text-gray-400 mb-4">
                  {habit.description}
                </Text>
              )}

              <View className="flex-row items-center space-x-4">
                <View className="flex-row items-center">
                  <Ionicons name="calendar" size={16} color="#6B7280" />
                  <Text className="text-sm text-gray-600 dark:text-gray-400 ml-1 capitalize">
                    {habit.frequency}
                  </Text>
                </View>
                <View className="flex-row items-center">
                  <Ionicons name="target" size={16} color="#6B7280" />
                  <Text className="text-sm text-gray-600 dark:text-gray-400 ml-1">
                    {habit.target_count} {habit.unit}
                  </Text>
                </View>
                <View className="flex-row items-center">
                  <Ionicons name="folder" size={16} color="#6B7280" />
                  <Text className="text-sm text-gray-600 dark:text-gray-400 ml-1 capitalize">
                    {habit.category}
                  </Text>
                </View>
              </View>
            </View>

            <TouchableOpacity
              onPress={handleToggleCompletion}
              className={`w-16 h-16 rounded-full border-2 items-center justify-center ${isCompletedToday
                ? 'bg-green-500 border-green-500'
                : 'border-gray-300 dark:border-gray-600'
                }`}
            >
              {isCompletedToday && (
                <Ionicons name="checkmark" size={32} color="white" />
              )}
            </TouchableOpacity>
          </View>

          {/* Today's Progress */}
          <View className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <Text className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
              Today's Progress
            </Text>
            <View className="flex-row items-center justify-between">
              <Text className="text-lg font-bold text-gray-900 dark:text-gray-100">
                {todayCount}/{habit.target_count} {habit.unit}
              </Text>
              <Text className="text-sm text-gray-600 dark:text-gray-400">
                {Math.round((todayCount / habit.target_count) * 100)}% complete
              </Text>
            </View>
            <View className="h-2 bg-gray-200 dark:bg-gray-600 rounded-full mt-2 overflow-hidden">
              <View
                className="h-full bg-green-500 rounded-full"
                style={{ width: `${Math.min((todayCount / habit.target_count) * 100, 100)}%` }}
              />
            </View>
          </View>
        </View>

        {/* Statistics */}
        {stats && !isLoadingStats ? (
          <HabitStatsCard stats={stats} title="Statistics" />
        ) : (
          <View className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm mb-6">
            <Text className="text-lg font-bold text-gray-900 dark:text-gray-100 mb-4">
              Statistics
            </Text>
            <View className="flex-row justify-center">
              <Text className="text-gray-600 dark:text-gray-400">Loading statistics...</Text>
            </View>
          </View>
        )}

        {/* Progress Chart */}
        {progressData.length > 0 && !isLoadingStats && (
          <View className="mb-6">
            <HabitProgressChart
              data={progressData}
              title="30-Day Progress"
              showDates={false}
            />
          </View>
        )}

        {/* Actions */}
        <View className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm mb-6">
          <Text className="text-lg font-bold text-gray-900 dark:text-gray-100 mb-4">
            Actions
          </Text>

          <TouchableOpacity
            onPress={handleEdit}
            className="flex-row items-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg mb-3"
          >
            <Ionicons name="create-outline" size={24} color="#3B82F6" />
            <Text className="text-blue-600 dark:text-blue-400 font-medium ml-3">
              Edit Habit
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={handleDelete}
            disabled={isDeleting}
            className="flex-row items-center p-4 bg-red-50 dark:bg-red-900/20 rounded-lg"
          >
            <Ionicons name="trash-outline" size={24} color="#EF4444" />
            <Text className="text-red-600 dark:text-red-400 font-medium ml-3">
              {isDeleting ? 'Deleting...' : 'Delete Habit'}
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
}
