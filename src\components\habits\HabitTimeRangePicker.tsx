import React from 'react';
import { Text, View } from 'react-native';
import { HabitTimeRange } from '../../types/habit.types';

interface HabitTimeRangePickerProps {
  timeRange?: HabitTimeRange;
  onTimeRangeChange: (timeRange: HabitTimeRange | undefined) => void;
}

const HabitTimeRangePicker: React.FC<HabitTimeRangePickerProps> = ({
  timeRange,
  onTimeRangeChange,
}) => {
  // Simplified version for testing
  return (
    <View className="mb-4">
      <Text className="text-base font-medium text-gray-900 dark:text-gray-100">
        Time Range (Coming Soon)
      </Text>
      <Text className="text-sm text-gray-500 dark:text-gray-400 mt-1">
        Time range selection will be available in a future update
      </Text>
    </View>
  );
};

export default HabitTimeRangePicker;
