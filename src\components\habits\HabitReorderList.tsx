import React, { useState } from 'react';
import { View, Text, TouchableOpacity, ScrollView, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Habit } from '../../database/entities/Habit';

interface HabitReorderListProps {
  habits: Habit[];
  onReorder: (habitIds: string[]) => Promise<void>;
  onClose: () => void;
  isVisible: boolean;
}

const HabitReorderList: React.FC<HabitReorderListProps> = ({
  habits,
  onReorder,
  onClose,
  isVisible,
}) => {
  const [orderedHabits, setOrderedHabits] = useState<Habit[]>(habits);
  const [isSaving, setIsSaving] = useState(false);

  React.useEffect(() => {
    setOrderedHabits(habits);
  }, [habits]);

  const moveHabit = (fromIndex: number, toIndex: number) => {
    const newOrder = [...orderedHabits];
    const [movedHabit] = newOrder.splice(fromIndex, 1);
    newOrder.splice(toIndex, 0, movedHabit);
    setOrderedHabits(newOrder);
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      const habitIds = orderedHabits.map(habit => habit.id);
      await onReorder(habitIds);
      onClose();
    } catch (error) {
      Alert.alert('Error', 'Failed to save habit order. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    setOrderedHabits(habits); // Reset to original order
    onClose();
  };

  if (!isVisible) {
    return null;
  }

  return (
    <View className="flex-1 bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <View className="bg-white dark:bg-gray-800 px-6 pt-12 pb-6 shadow-sm">
        <View className="flex-row items-center justify-between">
          <TouchableOpacity onPress={handleCancel}>
            <Text className="text-blue-600 dark:text-blue-400 font-medium">Cancel</Text>
          </TouchableOpacity>
          <Text className="text-xl font-bold text-gray-900 dark:text-gray-100">
            Reorder Habits
          </Text>
          <TouchableOpacity onPress={handleSave} disabled={isSaving}>
            <Text className={`font-medium ${isSaving ? 'text-gray-400' : 'text-blue-600 dark:text-blue-400'}`}>
              {isSaving ? 'Saving...' : 'Save'}
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Instructions */}
      <View className="px-6 py-4">
        <Text className="text-sm text-gray-600 dark:text-gray-400 text-center">
          Drag habits to reorder them. The new order will be saved when you tap "Save".
        </Text>
      </View>

      {/* Habit List */}
      <ScrollView className="flex-1 px-6">
        {orderedHabits.map((habit, index) => (
          <View
            key={habit.id}
            className="bg-white dark:bg-gray-800 rounded-xl p-4 mb-3 shadow-sm"
          >
            <View className="flex-row items-center">
              {/* Drag Handle */}
              <View className="mr-4">
                <Ionicons name="reorder-three" size={24} color="#6B7280" />
              </View>

              {/* Habit Info */}
              <View className="flex-1 flex-row items-center">
                <View
                  className="w-4 h-4 rounded-full mr-3"
                  style={{ backgroundColor: habit.color }}
                />
                <View className="flex-1">
                  <Text className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                    {habit.name}
                  </Text>
                  <Text className="text-sm text-gray-600 dark:text-gray-400 capitalize">
                    {habit.category} • {habit.frequency}
                  </Text>
                </View>
              </View>

              {/* Move Buttons */}
              <View className="flex-row space-x-2">
                <TouchableOpacity
                  onPress={() => moveHabit(index, Math.max(0, index - 1))}
                  disabled={index === 0}
                  className={`w-8 h-8 rounded-full items-center justify-center ${
                    index === 0
                      ? 'bg-gray-100 dark:bg-gray-700'
                      : 'bg-blue-100 dark:bg-blue-900/20'
                  }`}
                >
                  <Ionicons
                    name="chevron-up"
                    size={16}
                    color={index === 0 ? '#9CA3AF' : '#3B82F6'}
                  />
                </TouchableOpacity>

                <TouchableOpacity
                  onPress={() => moveHabit(index, Math.min(orderedHabits.length - 1, index + 1))}
                  disabled={index === orderedHabits.length - 1}
                  className={`w-8 h-8 rounded-full items-center justify-center ${
                    index === orderedHabits.length - 1
                      ? 'bg-gray-100 dark:bg-gray-700'
                      : 'bg-blue-100 dark:bg-blue-900/20'
                  }`}
                >
                  <Ionicons
                    name="chevron-down"
                    size={16}
                    color={index === orderedHabits.length - 1 ? '#9CA3AF' : '#3B82F6'}
                  />
                </TouchableOpacity>
              </View>
            </View>

            {/* Order Number */}
            <View className="absolute top-2 right-2">
              <View className="w-6 h-6 bg-gray-100 dark:bg-gray-700 rounded-full items-center justify-center">
                <Text className="text-xs font-bold text-gray-600 dark:text-gray-400">
                  {index + 1}
                </Text>
              </View>
            </View>
          </View>
        ))}
      </ScrollView>
    </View>
  );
};

export default HabitReorderList;
