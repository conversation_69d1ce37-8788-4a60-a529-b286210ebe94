import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useEffect, useState } from 'react';
import { ScrollView, Text, TouchableOpacity, View } from 'react-native';
import { useAppStore, useHabitStore, useUserStore } from '../../src/stores';

export default function HomeScreen() {
  const router = useRouter();
  const { currentUser, loadUser } = useUserStore();
  const { habits, loadHabits } = useHabitStore();
  const { setActiveTab } = useAppStore();
  const [todayCompletions, setTodayCompletions] = useState(0);

  useEffect(() => {
    setActiveTab('home');

    const initializeHome = async () => {
      if (!currentUser) {
        await loadUser();
      }

      if (currentUser) {
        await loadHabits(currentUser.id);
      }
    };

    initializeHome();
  }, [currentUser]);

  const today = new Date().toISOString().split('T')[0];
  const activeHabits = habits.filter(habit => habit.is_active);
  const completedToday = activeHabits.filter(habit =>
    habit.completions?.some(completion => completion.completed_date === today)
  ).length;

  const handleCreateHabit = () => {
    router.push('/habit-form');
  };

  const handleStartTimer = () => {
    router.push('/(tabs)/timer');
  };

  const handleViewHabits = () => {
    router.push('/(tabs)/habits');
  };

  return (
    <ScrollView className="flex-1 bg-gray-50 dark:bg-gray-900">
      <StatusBar style="auto" />
      {/* Header */}
      <View className="bg-white dark:bg-gray-800 px-6 pt-12 pb-6 shadow-sm">
        <Text className="text-2xl font-bold text-gray-900 dark:text-gray-100">
          Welcome back{currentUser ? `, ${currentUser.name.split(' ')[0]}` : ''}!
        </Text>
        <Text className="text-gray-600 dark:text-gray-400 mt-1">
          {new Date().toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          })}
        </Text>
      </View>

      {/* Today's Progress */}
      <View className="mx-6 mt-6">
        <View className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm">
          <View className="flex-row items-center justify-between mb-4">
            <Text className="text-lg font-semibold text-gray-900 dark:text-gray-100">Today's Progress</Text>
            <Ionicons name="trending-up" size={24} color="#3B82F6" />
          </View>

          <View className="flex-row items-center justify-between">
            <View className="flex-1">
              <Text className="text-3xl font-bold text-blue-600 dark:text-blue-400">
                {completedToday}/{activeHabits.length}
              </Text>
              <Text className="text-gray-600 dark:text-gray-400">Habits completed</Text>
            </View>

            <View className="w-20 h-20 rounded-full bg-blue-100 dark:bg-blue-900/20 items-center justify-center">
              <Text className="text-lg font-bold text-blue-600">
                {activeHabits.length > 0 ? Math.round((completedToday / activeHabits.length) * 100) : 0}%
              </Text>
            </View>
          </View>
        </View>
      </View>

      {/* Quick Actions */}
      <View className="mx-6 mt-6">
        <Text className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Quick Actions</Text>

        <View className="flex-row">
          <TouchableOpacity
            className="flex-1 bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm items-center mr-2"
            onPress={handleCreateHabit}
          >
            <View className="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-full items-center justify-center mb-2">
              <Ionicons name="add" size={24} color="#10B981" />
            </View>
            <Text className="text-sm font-medium text-gray-900 dark:text-gray-100">Add Habit</Text>
          </TouchableOpacity>

          <TouchableOpacity
            className="flex-1 bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm items-center mx-2"
            onPress={handleStartTimer}
          >
            <View className="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-full items-center justify-center mb-2">
              <Ionicons name="timer" size={24} color="#8B5CF6" />
            </View>
            <Text className="text-sm font-medium text-gray-900 dark:text-gray-100">Events</Text>
          </TouchableOpacity>

          <TouchableOpacity
            className="flex-1 bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm items-center ml-2"
            onPress={handleViewHabits}
          >
            <View className="w-12 h-12 bg-orange-100 dark:bg-orange-900/20 rounded-full items-center justify-center mb-2">
              <Ionicons name="stats-chart" size={24} color="#F59E0B" />
            </View>
            <Text className="text-sm font-medium text-gray-900 dark:text-gray-100">View Habits</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Recent Habits */}
      <View className="mx-6 mt-6 mb-6">
        <Text className="text-lg font-semibold text-gray-900 mb-4">Today's Habits</Text>

        {activeHabits.length === 0 ? (
          <View className="bg-white rounded-xl p-6 shadow-sm items-center">
            <Ionicons name="leaf-outline" size={48} color="#9CA3AF" />
            <Text className="text-gray-600 mt-2 text-center">
              No habits yet. Start building your routine!
            </Text>
          </View>
        ) : (
          <View className="space-y-3">
            {activeHabits.slice(0, 5).map((habit) => {
              const isCompletedToday = habit.completions?.some(
                completion => completion.completed_date === today
              );

              return (
                <View key={habit.id} className="bg-white rounded-xl p-4 shadow-sm flex-row items-center">
                  <View
                    className="w-4 h-4 rounded-full mr-3"
                    style={{ backgroundColor: habit.color }}
                  />
                  <View className="flex-1">
                    <Text className="font-medium text-gray-900">{habit.name}</Text>
                    <Text className="text-sm text-gray-600">{habit.category}</Text>
                  </View>
                  <View className={`w-6 h-6 rounded-full border-2 items-center justify-center ${isCompletedToday
                    ? 'bg-green-500 border-green-500'
                    : 'border-gray-300'
                    }`}>
                    {isCompletedToday && (
                      <Ionicons name="checkmark" size={16} color="white" />
                    )}
                  </View>
                </View>
              );
            })}
          </View>
        )}
      </View>
    </ScrollView>
  );
}
