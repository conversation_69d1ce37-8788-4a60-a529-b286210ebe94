import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Alert, Modal } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Habit } from '../../database/entities/Habit';

interface HabitBulkActionsProps {
  selectedHabits: Habit[];
  onDeleteSelected: (habitIds: string[]) => Promise<void>;
  onArchiveSelected: (habitIds: string[]) => Promise<void>;
  onClearSelection: () => void;
  onSelectAll: () => void;
  totalHabits: number;
  isVisible: boolean;
}

const HabitBulkActions: React.FC<HabitBulkActionsProps> = ({
  selectedHabits,
  onDeleteSelected,
  onArchiveSelected,
  onClearSelection,
  onSelectAll,
  totalHabits,
  isVisible,
}) => {
  const [isProcessing, setIsProcessing] = useState(false);

  const handleDeleteSelected = () => {
    Alert.alert(
      'Delete Habits',
      `Are you sure you want to delete ${selectedHabits.length} habit${selectedHabits.length > 1 ? 's' : ''}? This action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            setIsProcessing(true);
            try {
              await onDeleteSelected(selectedHabits.map(h => h.id));
              onClearSelection();
            } catch (error) {
              Alert.alert('Error', 'Failed to delete habits. Please try again.');
            } finally {
              setIsProcessing(false);
            }
          },
        },
      ]
    );
  };

  const handleArchiveSelected = () => {
    Alert.alert(
      'Archive Habits',
      `Are you sure you want to archive ${selectedHabits.length} habit${selectedHabits.length > 1 ? 's' : ''}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Archive',
          onPress: async () => {
            setIsProcessing(true);
            try {
              await onArchiveSelected(selectedHabits.map(h => h.id));
              onClearSelection();
            } catch (error) {
              Alert.alert('Error', 'Failed to archive habits. Please try again.');
            } finally {
              setIsProcessing(false);
            }
          },
        },
      ]
    );
  };

  if (!isVisible || selectedHabits.length === 0) {
    return null;
  }

  return (
    <View className="absolute bottom-0 left-0 right-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-4 shadow-lg">
      <View className="flex-row items-center justify-between mb-4">
        <Text className="text-lg font-bold text-gray-900 dark:text-gray-100">
          {selectedHabits.length} selected
        </Text>
        <TouchableOpacity onPress={onClearSelection}>
          <Ionicons name="close" size={24} color="#6B7280" />
        </TouchableOpacity>
      </View>

      <View className="flex-row space-x-3">
        {/* Select All */}
        {selectedHabits.length < totalHabits && (
          <TouchableOpacity
            onPress={onSelectAll}
            className="flex-1 flex-row items-center justify-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg"
          >
            <Ionicons name="checkmark-done" size={20} color="#3B82F6" />
            <Text className="text-blue-600 dark:text-blue-400 font-medium ml-2">
              Select All
            </Text>
          </TouchableOpacity>
        )}

        {/* Archive */}
        <TouchableOpacity
          onPress={handleArchiveSelected}
          disabled={isProcessing}
          className="flex-1 flex-row items-center justify-center p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg"
        >
          <Ionicons name="archive" size={20} color="#F59E0B" />
          <Text className="text-yellow-600 dark:text-yellow-400 font-medium ml-2">
            Archive
          </Text>
        </TouchableOpacity>

        {/* Delete */}
        <TouchableOpacity
          onPress={handleDeleteSelected}
          disabled={isProcessing}
          className="flex-1 flex-row items-center justify-center p-3 bg-red-50 dark:bg-red-900/20 rounded-lg"
        >
          <Ionicons name="trash" size={20} color="#EF4444" />
          <Text className="text-red-600 dark:text-red-400 font-medium ml-2">
            {isProcessing ? 'Processing...' : 'Delete'}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default HabitBulkActions;
