import { AppDataSource, initializeDatabase } from './config';
import {
  EventRepository,
  HabitCompletionRepository,
  HabitRepository,
  HabitStreakRepository,
  UserRepository,
} from './repositories';

export class DatabaseService {
  private static instance: DatabaseService;
  private isInitialized = false;

  public userRepository!: UserRepository;
  public habitRepository!: HabitRepository;
  public habitCompletionRepository!: HabitCompletionRepository;
  public habitStreakRepository!: HabitStreakRepository;
  public eventRepository!: EventRepository;

  private constructor() {}

  public static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      await initializeDatabase();

      // Initialize repositories
      this.userRepository = new UserRepository();
      this.habitRepository = new HabitRepository();
      this.habitCompletionRepository = new HabitCompletionRepository();
      this.habitStreakRepository = new HabitStreakRepository();
      this.eventRepository = new EventRepository();

      this.isInitialized = true;
      if (__DEV__) {
        console.log('DatabaseService initialized successfully');
      }
    } catch (error) {
      console.error('Failed to initialize DatabaseService:', error);
      throw error;
    }
  }

  public async createDefaultUser(): Promise<void> {
    try {
      const existingUser = await this.userRepository.findByEmail('<EMAIL>');
      if (!existingUser) {
        await this.userRepository.create({
          name: 'WinArc User',
          email: '<EMAIL>',
          theme: 'light',
          notifications_enabled: true,
          language: 'en',
          timezone: 'UTC',
        });
        if (__DEV__) {
          console.log('Default user created');
        }
      }
    } catch (error) {
      console.error('Error creating default user:', error);
    }
  }

  public isReady(): boolean {
    return this.isInitialized && AppDataSource.isInitialized;
  }
}

// Export singleton instance
export const databaseService = DatabaseService.getInstance();
