import { Ionicons } from '@expo/vector-icons';
import { useEffect, useRef, useState } from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import { useAppStore, useTimerStore } from '../../src/stores';
import { useEventStore } from '../../src/stores/eventStore';

export default function EventsScreen() {
  const {
    type,
    status,
    timeRemaining,
    totalTime,
    pomodoroPhase,
    pomodoroSession,
    startTimer,
    pauseTimer,
    resumeTimer,
    resetTimer,
    tick,
    startPomodoro,
    setTimerType,
  } = useTimerStore();

  const {
    upcomingEvents,
    isLoading: eventsLoading,
    fetchUpcomingEvents
  } = useEventStore();

  const { setActiveTab } = useAppStore();
  const intervalRef = useRef<number | null>(null);
  const [activeSection, setActiveSection] = useState<'pomodoro' | 'events'>('pomodoro');

  useEffect(() => {
    setActiveTab('timer');
    // Fetch upcoming events for the default user
    fetchUpcomingEvents('default-user', 5);
  }, []);

  useEffect(() => {
    if (status === 'running') {
      intervalRef.current = setInterval(() => {
        tick();
      }, 1000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [status, tick]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getProgress = () => {
    if (totalTime === 0) return 0;
    return type === 'stopwatch' ? 1 : (totalTime - timeRemaining) / totalTime;
  };

  const getPhaseColor = () => {
    switch (pomodoroPhase) {
      case 'work':
        return '#EF4444'; // red
      case 'break':
        return '#10B981'; // green
      case 'longBreak':
        return '#3B82F6'; // blue
      default:
        return '#6B7280'; // gray
    }
  };

  const getPhaseText = () => {
    switch (pomodoroPhase) {
      case 'work':
        return 'Focus Time';
      case 'break':
        return 'Short Break';
      case 'longBreak':
        return 'Long Break';
      default:
        return 'Timer';
    }
  };

  return (
    <View className="flex-1 bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <View className="bg-white dark:bg-gray-800 px-6 pt-12 pb-6 shadow-sm">
        <Text className="text-2xl font-bold text-gray-900 dark:text-gray-100">Events</Text>
        <Text className="text-gray-600 dark:text-gray-400 mt-1">
          Pomodoro Timer & Event Countdown
        </Text>
      </View>

      {/* Section Tabs */}
      <View className="flex-row bg-white dark:bg-gray-800 mx-6 mt-6 rounded-xl shadow-sm">
        <TouchableOpacity
          onPress={() => setActiveSection('pomodoro')}
          className={`flex-1 py-3 rounded-xl ${activeSection === 'pomodoro'
            ? 'bg-blue-500'
            : 'bg-transparent'
            }`}
        >
          <Text className={`text-center font-medium ${activeSection === 'pomodoro'
            ? 'text-white'
            : 'text-gray-600 dark:text-gray-400'
            }`}>
            Pomodoro
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => setActiveSection('events')}
          className={`flex-1 py-3 rounded-xl ${activeSection === 'events'
            ? 'bg-blue-500'
            : 'bg-transparent'
            }`}
        >
          <Text className={`text-center font-medium ${activeSection === 'events'
            ? 'text-white'
            : 'text-gray-600 dark:text-gray-400'
            }`}>
            Events
          </Text>
        </TouchableOpacity>
      </View>

      {/* Timer Display */}
      <View className="flex-1 items-center justify-center px-6">
        <View className="items-center mb-8">
          {/* Circular Progress */}
          <View className="relative w-80 h-80 items-center justify-center">
            <View
              className="absolute w-full h-full rounded-full border-8"
              style={{
                borderColor: type === 'pomodoro' ? getPhaseColor() : '#3B82F6',
                opacity: 0.2
              }}
            />
            <View
              className="absolute w-full h-full rounded-full border-8 border-transparent"
              style={{
                borderTopColor: type === 'pomodoro' ? getPhaseColor() : '#3B82F6',
                transform: [{ rotate: `${getProgress() * 360}deg` }]
              }}
            />

            {/* Time Display */}
            <View className="items-center">
              <Text className="text-6xl font-light text-gray-900 mb-2">
                {formatTime(timeRemaining)}
              </Text>
              {type === 'pomodoro' && (
                <Text
                  className="text-lg font-medium"
                  style={{ color: getPhaseColor() }}
                >
                  {getPhaseText()}
                </Text>
              )}
            </View>
          </View>
        </View>

        {activeSection === 'pomodoro' ? (
          /* Pomodoro Section */
          <View>
            {/* Control Buttons */}
            <View className="flex-row items-center justify-center mt-8">
              <TouchableOpacity
                onPress={resetTimer}
                className="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-full items-center justify-center mr-6"
              >
                <Ionicons name="refresh" size={24} color="#6B7280" />
              </TouchableOpacity>

              <TouchableOpacity
                onPress={() => {
                  if (status === 'running') {
                    pauseTimer();
                  } else if (status === 'paused') {
                    resumeTimer();
                  } else {
                    startPomodoro();
                  }
                }}
                className="w-20 h-20 rounded-full items-center justify-center mx-6"
                style={{
                  backgroundColor: getPhaseColor()
                }}
              >
                <Ionicons
                  name={status === 'running' ? 'pause' : 'play'}
                  size={32}
                  color="white"
                />
              </TouchableOpacity>

              <TouchableOpacity
                onPress={() => {
                  if (status === 'running' || status === 'paused') {
                    resetTimer();
                  }
                }}
                className="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-full items-center justify-center ml-6"
              >
                <Ionicons name="play-skip-forward" size={24} color="#6B7280" />
              </TouchableOpacity>
            </View>

            {/* Pomodoro Phase Indicator */}
            <View className="mt-8 bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm">
              <Text className="text-center text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                Pomodoro Timer
              </Text>
              <Text className="text-center text-sm text-gray-600 dark:text-gray-400">
                {pomodoroPhase === 'work' ? 'Focus Time' : pomodoroPhase === 'break' ? 'Short Break' : 'Long Break'}
              </Text>
              <Text className="text-center text-xs text-gray-500 dark:text-gray-500 mt-1">
                Session {pomodoroSession}
              </Text>
            </View>
          </View>
        ) : (
          /* Events Section */
          <View className="mt-8">
            <Text className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-4">
              Upcoming Events
            </Text>

            {eventsLoading ? (
              <View className="items-center justify-center py-12">
                <Text className="text-gray-600 dark:text-gray-400">Loading events...</Text>
              </View>
            ) : upcomingEvents.length === 0 ? (
              <View className="items-center justify-center py-12">
                <Ionicons name="calendar-outline" size={64} color="#9CA3AF" />
                <Text className="text-gray-600 dark:text-gray-400 mt-4 text-center">
                  No upcoming events
                </Text>
                <Text className="text-gray-500 dark:text-gray-500 text-sm text-center mt-2">
                  Add birthdays, anniversaries, and holidays to track
                </Text>
              </View>
            ) : (
              <View className="space-y-3">
                {upcomingEvents.map((event) => (
                  <View key={event.id} className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm">
                    <View className="flex-row items-center justify-between">
                      <View className="flex-1">
                        <Text className="font-semibold text-gray-900 dark:text-gray-100">
                          {event.name}
                        </Text>
                        <Text className="text-sm text-gray-600 dark:text-gray-400 capitalize">
                          {event.type}
                        </Text>
                      </View>
                      <View className="items-end">
                        <Text className="text-2xl font-bold text-blue-500">
                          {event.daysRemaining}
                        </Text>
                        <Text className="text-xs text-gray-500 dark:text-gray-400">
                          {event.daysRemaining === 0 ? 'Today!' : event.daysRemaining === 1 ? 'day left' : 'days left'}
                        </Text>
                      </View>
                    </View>
                  </View>
                ))}
              </View>
            )}
          </View>
        )}
      </View>
    </View>
  );
}
