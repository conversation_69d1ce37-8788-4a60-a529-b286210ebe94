import { View, Text, TouchableOpacity } from 'react-native';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useAppStore } from '../src/stores';

export default function OnboardingScreen() {
  const { setFirstLaunch, setOnboardingCompleted } = useAppStore();

  const handleGetStarted = () => {
    setFirstLaunch(false);
    setOnboardingCompleted(true);
    router.replace('/(tabs)');
  };

  return (
    <View className="flex-1 bg-white">
      {/* Content */}
      <View className="flex-1 items-center justify-center px-8">
        {/* Logo/Icon */}
        <View className="w-32 h-32 bg-blue-500 rounded-full items-center justify-center mb-8">
          <Ionicons name="checkmark-circle" size={64} color="white" />
        </View>

        {/* Title */}
        <Text className="text-3xl font-bold text-gray-900 text-center mb-4">
          Welcome to WinArc
        </Text>

        {/* Subtitle */}
        <Text className="text-lg text-gray-600 text-center mb-12 leading-6">
          Build lasting habits, track your progress, and achieve your goals with our powerful habit tracker.
        </Text>

        {/* Features */}
        <View className="w-full space-y-4 mb-12">
          <View className="flex-row items-center">
            <View className="w-10 h-10 bg-green-100 rounded-full items-center justify-center mr-4">
              <Ionicons name="checkmark" size={20} color="#10B981" />
            </View>
            <Text className="text-gray-700 flex-1">Track daily habits and build streaks</Text>
          </View>

          <View className="flex-row items-center">
            <View className="w-10 h-10 bg-purple-100 rounded-full items-center justify-center mr-4">
              <Ionicons name="timer" size={20} color="#8B5CF6" />
            </View>
            <Text className="text-gray-700 flex-1">Focus with Pomodoro timer</Text>
          </View>

          <View className="flex-row items-center">
            <View className="w-10 h-10 bg-orange-100 rounded-full items-center justify-center mr-4">
              <Ionicons name="stats-chart" size={20} color="#F59E0B" />
            </View>
            <Text className="text-gray-700 flex-1">Visualize your progress with insights</Text>
          </View>

          <View className="flex-row items-center">
            <View className="w-10 h-10 bg-blue-100 rounded-full items-center justify-center mr-4">
              <Ionicons name="heart" size={20} color="#3B82F6" />
            </View>
            <Text className="text-gray-700 flex-1">Practice mindfulness and breathing</Text>
          </View>
        </View>
      </View>

      {/* Get Started Button */}
      <View className="px-8 pb-12">
        <TouchableOpacity
          onPress={handleGetStarted}
          className="bg-blue-500 py-4 rounded-xl items-center shadow-sm"
        >
          <Text className="text-white text-lg font-semibold">Get Started</Text>
        </TouchableOpacity>

        <Text className="text-center text-gray-500 text-sm mt-4">
          By continuing, you agree to our Terms of Service and Privacy Policy
        </Text>
      </View>
    </View>
  );
}
