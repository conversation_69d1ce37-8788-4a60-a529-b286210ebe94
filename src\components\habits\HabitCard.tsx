import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import { Habit } from '../../database/entities/Habit';
import { HabitCompletion } from '../../database/entities/HabitCompletion';

interface HabitCardProps {
  habit: Habit & { completions?: HabitCompletion[] };
  onToggleCompletion: (habitId: string) => void;
  onPress?: (habit: Habit) => void;
  onLongPress?: (habit: Habit) => void;
  showProgress?: boolean;
  compact?: boolean;
  // Selection mode props
  selectionMode?: boolean;
  isSelected?: boolean;
  onToggleSelection?: (habit: Habit) => void;
}

const HabitCard: React.FC<HabitCardProps> = ({
  habit,
  onToggleCompletion,
  onPress,
  onLongPress,
  showProgress = true,
  compact = false,
  // Selection mode props
  selectionMode = false,
  isSelected = false,
  onToggleSelection,
}) => {
  const today = new Date().toISOString().split('T')[0];

  const isCompletedToday = habit.completions?.some(
    completion => completion.completed_date === today
  ) || false;

  const todayCompletion = habit.completions?.find(
    completion => completion.completed_date === today
  );

  const completionCount = todayCompletion?.count || 0;
  const progressPercentage = Math.min((completionCount / habit.target_count) * 100, 100);

  // Calculate current streak (simplified - in real app this would come from HabitStreak entity)
  const getCurrentStreak = (): number => {
    if (!habit.completions || habit.completions.length === 0) return 0;

    const sortedCompletions = habit.completions
      .sort((a, b) => new Date(b.completed_date).getTime() - new Date(a.completed_date).getTime());

    let streak = 0;
    let currentDate = new Date();

    for (const completion of sortedCompletions) {
      const completionDate = new Date(completion.completed_date);
      const daysDiff = Math.floor((currentDate.getTime() - completionDate.getTime()) / (1000 * 60 * 60 * 24));

      if (daysDiff === streak) {
        streak++;
        currentDate = completionDate;
      } else {
        break;
      }
    }

    return streak;
  };

  const currentStreak = getCurrentStreak();

  const handlePress = () => {
    if (selectionMode && onToggleSelection) {
      onToggleSelection(habit);
    } else if (onPress) {
      onPress(habit);
    }
  };

  const handleLongPress = () => {
    if (onLongPress) {
      onLongPress(habit);
    }
  };

  if (compact) {
    return (
      <TouchableOpacity
        onPress={handlePress}
        onLongPress={handleLongPress}
        className="bg-white dark:bg-gray-800 rounded-xl p-3 shadow-sm flex-row items-center"
      >
        <View
          className="w-3 h-3 rounded-full mr-3"
          style={{ backgroundColor: habit.color }}
        />
        <View className="flex-1">
          <Text className="font-medium text-gray-900 dark:text-gray-100 text-sm">
            {habit.name}
          </Text>
          <Text className="text-xs text-gray-600 dark:text-gray-400">
            {habit.category}
          </Text>
        </View>
        <TouchableOpacity
          onPress={() => onToggleCompletion(habit.id)}
          className={`w-6 h-6 rounded-full border-2 items-center justify-center ${isCompletedToday
            ? 'bg-green-500 border-green-500'
            : 'border-gray-300 dark:border-gray-600'
            }`}
        >
          {isCompletedToday && (
            <Ionicons name="checkmark" size={12} color="white" />
          )}
        </TouchableOpacity>
      </TouchableOpacity>
    );
  }

  return (
    <TouchableOpacity
      onPress={handlePress}
      onLongPress={handleLongPress}
      className={`bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm mb-3 ${selectionMode && isSelected ? 'border-2 border-blue-500' : ''
        }`}
    >
      <View className="flex-row items-start justify-between">
        {/* Selection indicator */}
        {selectionMode && (
          <View className="mr-3 mt-1">
            <View
              className={`w-6 h-6 rounded-full border-2 items-center justify-center ${isSelected
                  ? 'bg-blue-500 border-blue-500'
                  : 'border-gray-300 dark:border-gray-600'
                }`}
            >
              {isSelected && (
                <Ionicons name="checkmark" size={16} color="white" />
              )}
            </View>
          </View>
        )}

        <View className="flex-1 mr-4">
          {/* Header */}
          <View className="flex-row items-center mb-2">
            <View
              className="w-4 h-4 rounded-full mr-3"
              style={{ backgroundColor: habit.color }}
            />
            <Text className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex-1">
              {habit.name}
            </Text>
          </View>

          {/* Description */}
          {habit.description && (
            <Text className="text-gray-600 dark:text-gray-400 text-sm mb-2">
              {habit.description}
            </Text>
          )}

          {/* Stats Row */}
          <View className="flex-row items-center space-x-4 mb-2">
            <View className="flex-row items-center">
              <Ionicons name="flame" size={16} color="#F97316" />
              <Text className="text-sm text-gray-600 dark:text-gray-400 ml-1">
                {currentStreak} day streak
              </Text>
            </View>
            <View className="flex-row items-center">
              <Ionicons name="calendar" size={16} color="#6B7280" />
              <Text className="text-sm text-gray-600 dark:text-gray-400 ml-1 capitalize">
                {habit.frequency}
              </Text>
            </View>
          </View>

          {/* Progress */}
          {showProgress && (
            <View className="mb-2">
              <View className="flex-row items-center justify-between mb-1">
                <Text className="text-sm text-gray-600 dark:text-gray-400">
                  Progress: {completionCount}/{habit.target_count} {habit.unit}
                </Text>
                <Text className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  {Math.round(progressPercentage)}%
                </Text>
              </View>
              <View className="h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                <View
                  className="h-full bg-green-500 rounded-full transition-all duration-300"
                  style={{ width: `${progressPercentage}%` }}
                />
              </View>
            </View>
          )}
        </View>

        {/* Completion Button */}
        <TouchableOpacity
          onPress={() => onToggleCompletion(habit.id)}
          className={`w-12 h-12 rounded-full border-2 items-center justify-center ${isCompletedToday
            ? 'bg-green-500 border-green-500'
            : 'border-gray-300 dark:border-gray-600'
            }`}
        >
          {isCompletedToday && (
            <Ionicons name="checkmark" size={24} color="white" />
          )}
        </TouchableOpacity>
      </View>

      {/* Footer */}
      <View className="mt-4 pt-4 border-t border-gray-100 dark:border-gray-700">
        <View className="flex-row items-center justify-between">
          <Text className="text-sm text-gray-600 dark:text-gray-400">
            Target: {habit.target_count} {habit.unit} {habit.frequency}
          </Text>
          <TouchableOpacity>
            <Ionicons name="ellipsis-horizontal" size={20} color="#6B7280" />
          </TouchableOpacity>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default HabitCard;
