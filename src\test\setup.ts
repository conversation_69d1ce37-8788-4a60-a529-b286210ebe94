/**
 * Test Setup Configuration
 * Sets up testing environment for WinArc habit management functionality
 */

import '@testing-library/jest-native/extend-expect';
import 'react-native-gesture-handler/jestSetup';

// Mock React Native modules
jest.mock('react-native/Libraries/Animated/NativeAnimatedHelper');

// Mock Expo modules
jest.mock('expo-status-bar', () => ({
  StatusBar: 'StatusBar',
}));

jest.mock('expo-router', () => ({
  useRouter: () => ({
    push: jest.fn(),
    back: jest.fn(),
    replace: jest.fn(),
  }),
  useLocalSearchParams: () => ({}),
  Redirect: 'Redirect',
  Stack: {
    Screen: 'StackScreen',
  },
  Tabs: 'Tabs',
}));

jest.mock('expo-sqlite', () => ({
  openDatabase: jest.fn(() => ({
    transaction: jest.fn(),
    readTransaction: jest.fn(),
    close: jest.fn(),
  })),
}));

// Mock Ionicons
jest.mock('@expo/vector-icons', () => ({
  Ionicons: 'Ionicons',
}));

// Mock NativeWind
jest.mock('nativewind', () => ({
  styled: (component: any) => component,
}));

// Mock Zustand stores
jest.mock('../stores', () => ({
  useAppStore: () => ({
    isFirstLaunch: false,
    hasCompletedOnboarding: true,
    activeTab: 'habits',
    setActiveTab: jest.fn(),
  }),
  useUserStore: () => ({
    currentUser: {
      id: 'test-user-id',
      name: 'Test User',
      email: '<EMAIL>',
    },
    loadUser: jest.fn(),
  }),
  useHabitStore: () => ({
    habits: [],
    loadHabits: jest.fn(),
    createHabit: jest.fn(),
    updateHabit: jest.fn(),
    deleteHabit: jest.fn(),
  }),
}));

// Mock database service
jest.mock('../database', () => ({
  databaseService: {
    initialize: jest.fn(),
    createDefaultUser: jest.fn(),
    getRepository: jest.fn(),
  },
}));

// Mock theme provider
jest.mock('../components', () => ({
  ThemeProvider: ({ children }: { children: React.ReactNode }) => children,
  useResolvedTheme: () => 'light',
}));

// Global test utilities
global.console = {
  ...console,
  // Suppress console.log in tests unless explicitly needed
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Mock timers for testing
jest.useFakeTimers();

// Setup test environment
beforeEach(() => {
  jest.clearAllMocks();
});

afterEach(() => {
  jest.runOnlyPendingTimers();
  jest.useRealTimers();
  jest.useFakeTimers();
});
