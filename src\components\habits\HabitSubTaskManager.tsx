import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { HabitSubTask } from '../../types/habit.types';

interface HabitSubTaskManagerProps {
  subTasks: HabitSubTask[];
  onSubTasksChange: (subTasks: HabitSubTask[]) => void;
}

const HabitSubTaskManager: React.FC<HabitSubTaskManagerProps> = ({
  subTasks,
  onSubTasksChange,
}) => {
  const [newTaskName, setNewTaskName] = useState('');
  const [newTaskDescription, setNewTaskDescription] = useState('');
  const [editingTask, setEditingTask] = useState<string | null>(null);

  const generateId = () => {
    return Date.now().toString() + Math.random().toString(36).substr(2, 9);
  };

  const addSubTask = () => {
    if (!newTaskName.trim()) {
      Alert.alert('Error', 'Please enter a task name');
      return;
    }

    const newTask: HabitSubTask = {
      id: generateId(),
      name: newTaskName.trim(),
      description: newTaskDescription.trim(),
      order: subTasks.length,
    };

    onSubTasksChange([...subTasks, newTask]);
    setNewTaskName('');
    setNewTaskDescription('');
  };

  const updateSubTask = (id: string, updates: Partial<HabitSubTask>) => {
    const updatedTasks = subTasks.map(task =>
      task.id === id ? { ...task, ...updates } : task
    );
    onSubTasksChange(updatedTasks);
  };

  const deleteSubTask = (id: string) => {
    Alert.alert(
      'Delete Sub-task',
      'Are you sure you want to delete this sub-task?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            const filteredTasks = subTasks.filter(task => task.id !== id);
            // Reorder remaining tasks
            const reorderedTasks = filteredTasks.map((task, index) => ({
              ...task,
              order: index,
            }));
            onSubTasksChange(reorderedTasks);
          },
        },
      ]
    );
  };

  const moveSubTask = (id: string, direction: 'up' | 'down') => {
    const currentIndex = subTasks.findIndex(task => task.id === id);
    if (
      (direction === 'up' && currentIndex === 0) ||
      (direction === 'down' && currentIndex === subTasks.length - 1)
    ) {
      return;
    }

    const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;
    const reorderedTasks = [...subTasks];
    [reorderedTasks[currentIndex], reorderedTasks[newIndex]] = [
      reorderedTasks[newIndex],
      reorderedTasks[currentIndex],
    ];

    // Update order values
    const finalTasks = reorderedTasks.map((task, index) => ({
      ...task,
      order: index,
    }));

    onSubTasksChange(finalTasks);
  };

  return (
    <View>
      <Text className="text-base font-medium text-gray-900 dark:text-gray-100 mb-3">
        Sub-tasks
      </Text>
      <Text className="text-sm text-gray-600 dark:text-gray-400 mb-4">
        Break down your habit into smaller, manageable steps
      </Text>

      {/* Existing Sub-tasks */}
      {subTasks.map((task, index) => (
        <View
          key={task.id}
          className="bg-white dark:bg-gray-800 rounded-xl p-4 mb-3 border border-gray-200 dark:border-gray-700"
        >
          {editingTask === task.id ? (
            <View>
              <TextInput
                value={task.name}
                onChangeText={(text) => updateSubTask(task.id!, { name: text })}
                placeholder="Task name"
                placeholderTextColor="#9CA3AF"
                className="bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg px-3 py-2 text-gray-900 dark:text-gray-100 mb-3"
                maxLength={100}
              />
              <TextInput
                value={task.description}
                onChangeText={(text) => updateSubTask(task.id!, { description: text })}
                placeholder="Task description"
                placeholderTextColor="#9CA3AF"
                className="bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg px-3 py-2 text-gray-900 dark:text-gray-100 mb-3"
                multiline
                numberOfLines={2}
                maxLength={200}
              />
              <View className="flex-row justify-end space-x-2">
                <TouchableOpacity
                  onPress={() => setEditingTask(null)}
                  className="px-3 py-1 bg-gray-200 dark:bg-gray-600 rounded-lg"
                >
                  <Text className="text-gray-700 dark:text-gray-300 text-sm">Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() => setEditingTask(null)}
                  className="px-3 py-1 bg-blue-500 rounded-lg"
                >
                  <Text className="text-white text-sm">Save</Text>
                </TouchableOpacity>
              </View>
            </View>
          ) : (
            <View>
              <View className="flex-row items-start justify-between mb-2">
                <View className="flex-1 mr-3">
                  <Text className="font-medium text-gray-900 dark:text-gray-100 mb-1">
                    {index + 1}. {task.name}
                  </Text>
                  {task.description && (
                    <Text className="text-sm text-gray-600 dark:text-gray-400">
                      {task.description}
                    </Text>
                  )}
                </View>
                <View className="flex-row space-x-1">
                  <TouchableOpacity
                    onPress={() => moveSubTask(task.id!, 'up')}
                    disabled={index === 0}
                    className={`p-1 rounded ${
                      index === 0 ? 'opacity-30' : ''
                    }`}
                  >
                    <Ionicons name="chevron-up" size={16} color="#6B7280" />
                  </TouchableOpacity>
                  <TouchableOpacity
                    onPress={() => moveSubTask(task.id!, 'down')}
                    disabled={index === subTasks.length - 1}
                    className={`p-1 rounded ${
                      index === subTasks.length - 1 ? 'opacity-30' : ''
                    }`}
                  >
                    <Ionicons name="chevron-down" size={16} color="#6B7280" />
                  </TouchableOpacity>
                  <TouchableOpacity
                    onPress={() => setEditingTask(task.id!)}
                    className="p-1 rounded"
                  >
                    <Ionicons name="pencil" size={16} color="#6B7280" />
                  </TouchableOpacity>
                  <TouchableOpacity
                    onPress={() => deleteSubTask(task.id!)}
                    className="p-1 rounded"
                  >
                    <Ionicons name="trash" size={16} color="#EF4444" />
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          )}
        </View>
      ))}

      {/* Add New Sub-task */}
      <View className="bg-gray-50 dark:bg-gray-800 rounded-xl p-4 border-2 border-dashed border-gray-300 dark:border-gray-600">
        <TextInput
          value={newTaskName}
          onChangeText={setNewTaskName}
          placeholder="Add a new sub-task..."
          placeholderTextColor="#9CA3AF"
          className="bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg px-3 py-2 text-gray-900 dark:text-gray-100 mb-3"
          maxLength={100}
        />
        <TextInput
          value={newTaskDescription}
          onChangeText={setNewTaskDescription}
          placeholder="Description (optional)"
          placeholderTextColor="#9CA3AF"
          className="bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg px-3 py-2 text-gray-900 dark:text-gray-100 mb-3"
          multiline
          numberOfLines={2}
          maxLength={200}
        />
        <TouchableOpacity
          onPress={addSubTask}
          className="bg-blue-500 rounded-lg py-2 px-4 flex-row items-center justify-center"
        >
          <Ionicons name="add" size={20} color="white" />
          <Text className="text-white font-medium ml-2">Add Sub-task</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default HabitSubTaskManager;
