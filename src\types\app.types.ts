/**
 * Type definitions for general app-related data structures
 */

import type { Language, Theme } from './user.types';

export interface AppConfig {
  version: string;
  build_number: string;
  environment: 'development' | 'staging' | 'production';
  api_base_url: string;
  features: {
    analytics_enabled: boolean;
    push_notifications_enabled: boolean;
    social_features_enabled: boolean;
    premium_features_enabled: boolean;
  };
}

export interface AppState {
  isLoading: boolean;
  isOnline: boolean;
  activeTab: string;
  theme: Theme;
  language: Language;
  isFirstLaunch: boolean;
  hasCompletedOnboarding: boolean;
  notificationsEnabled: boolean;
  reminderNotificationsEnabled: boolean;
  lastSyncTime?: string;
  error?: string;
}

export interface NavigationState {
  currentRoute: string;
  previousRoute?: string;
  params?: Record<string, any>;
}

export interface ErrorState {
  hasError: boolean;
  error?: Error;
  errorBoundary?: boolean;
}

export interface LoadingState {
  isLoading: boolean;
  loadingMessage?: string;
  progress?: number;
}

export interface NotificationPayload {
  id: string;
  title: string;
  body: string;
  data?: Record<string, any>;
  scheduled_for?: string;
  type: 'habit_reminder' | 'timer_complete' | 'streak_milestone' | 'weekly_report' | 'general';
}

export interface AppPermissions {
  notifications: boolean;
  camera: boolean;
  storage: boolean;
  location: boolean;
}

export interface DeviceInfo {
  platform: 'ios' | 'android' | 'web';
  version: string;
  model?: string;
  screen_width: number;
  screen_height: number;
  is_tablet: boolean;
  has_notch: boolean;
}

export interface AppMetrics {
  session_start: string;
  session_duration: number; // seconds
  screens_visited: string[];
  actions_performed: Array<{
    action: string;
    timestamp: string;
    data?: Record<string, any>;
  }>;
  errors_encountered: Array<{
    error: string;
    timestamp: string;
    stack?: string;
  }>;
}

export interface FeatureFlag {
  name: string;
  enabled: boolean;
  description?: string;
  rollout_percentage?: number;
  target_users?: string[];
}

export interface AppUpdate {
  version: string;
  build_number: string;
  release_notes: string;
  is_required: boolean;
  download_url?: string;
  release_date: string;
}

export interface BackupData {
  version: string;
  created_at: string;
  user_data: any;
  habits_data: any[];
  completions_data: any[];
  streaks_data: any[];
  timer_sessions_data: any[];
  settings_data: any;
}

export interface SyncStatus {
  last_sync: string;
  is_syncing: boolean;
  sync_progress: number;
  pending_changes: number;
  conflicts: Array<{
    type: string;
    local_data: any;
    remote_data: any;
    timestamp: string;
  }>;
}

export interface AppAnalytics {
  daily_active_users: number;
  weekly_active_users: number;
  monthly_active_users: number;
  retention_rate: {
    day_1: number;
    day_7: number;
    day_30: number;
  };
  feature_usage: Array<{
    feature: string;
    usage_count: number;
    unique_users: number;
  }>;
  crash_rate: number;
  average_session_length: number;
  most_used_features: string[];
}

export interface AppFeedback {
  id: string;
  user_id: string;
  type: 'bug_report' | 'feature_request' | 'general_feedback' | 'rating';
  title: string;
  description: string;
  rating?: number;
  attachments?: string[];
  device_info: DeviceInfo;
  app_version: string;
  created_at: string;
  status: 'open' | 'in_progress' | 'resolved' | 'closed';
}
