import React from 'react';
import { View, Text, ScrollView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface HabitProgressChartProps {
  data: Array<{ date: string; completed: boolean; count: number }>;
  title?: string;
  showDates?: boolean;
  compact?: boolean;
}

const HabitProgressChart: React.FC<HabitProgressChartProps> = ({
  data,
  title = 'Progress',
  showDates = false,
  compact = false,
}) => {
  const maxCount = Math.max(...data.map(d => d.count), 1);
  
  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric' 
    });
  };

  const getDayOfWeek = (dateStr: string) => {
    const date = new Date(dateStr);
    return date.toLocaleDateString('en-US', { weekday: 'short' });
  };

  if (compact) {
    return (
      <View className="mb-4">
        <Text className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
          {title}
        </Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View className="flex-row space-x-1">
            {data.map((item, index) => (
              <View key={index} className="items-center">
                <View
                  className={`w-6 h-6 rounded ${
                    item.completed
                      ? 'bg-green-500'
                      : 'bg-gray-200 dark:bg-gray-700'
                  }`}
                />
                {showDates && (
                  <Text className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                    {getDayOfWeek(item.date)}
                  </Text>
                )}
              </View>
            ))}
          </View>
        </ScrollView>
      </View>
    );
  }

  return (
    <View className="mb-6">
      <Text className="text-lg font-bold text-gray-900 dark:text-gray-100 mb-4">
        {title}
      </Text>
      
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        <View className="flex-row items-end space-x-2 pb-4">
          {data.map((item, index) => {
            const height = Math.max((item.count / maxCount) * 100, 4);
            
            return (
              <View key={index} className="items-center">
                {/* Bar */}
                <View className="relative mb-2" style={{ height: 120 }}>
                  <View
                    className={`w-8 rounded-t ${
                      item.completed
                        ? 'bg-green-500'
                        : 'bg-gray-200 dark:bg-gray-700'
                    }`}
                    style={{ 
                      height: `${height}%`,
                      position: 'absolute',
                      bottom: 0,
                      width: 32,
                    }}
                  />
                  
                  {/* Count label */}
                  {item.count > 0 && (
                    <View className="absolute -top-6 left-1/2 transform -translate-x-1/2">
                      <Text className="text-xs font-medium text-gray-900 dark:text-gray-100">
                        {item.count}
                      </Text>
                    </View>
                  )}
                </View>
                
                {/* Date label */}
                <Text className="text-xs text-gray-600 dark:text-gray-400 text-center">
                  {showDates ? formatDate(item.date) : getDayOfWeek(item.date)}
                </Text>
              </View>
            );
          })}
        </View>
      </ScrollView>
      
      {/* Legend */}
      <View className="flex-row items-center justify-center space-x-4 mt-4">
        <View className="flex-row items-center">
          <View className="w-3 h-3 bg-green-500 rounded mr-2" />
          <Text className="text-sm text-gray-600 dark:text-gray-400">Completed</Text>
        </View>
        <View className="flex-row items-center">
          <View className="w-3 h-3 bg-gray-200 dark:bg-gray-700 rounded mr-2" />
          <Text className="text-sm text-gray-600 dark:text-gray-400">Missed</Text>
        </View>
      </View>
    </View>
  );
};

export default HabitProgressChart;
