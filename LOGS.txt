 <PERSON><PERSON><PERSON>  Attempted to import the module "E:\App Development\WinArc\node_modules\typeorm" which is listed in the "exports" of "E:\App Development\WinArc\node_modules\typeorm", however no match was resolved for this request (platform = android). Falling back to file-based resolution. Consider updating the call site or asking the package maintainer(s) to expose this API.
 <PERSON>R<PERSON>  Attempted to import the module "E:\App Development\WinArc\node_modules\typeorm" which is listed in the "exports" of "E:\App Development\WinArc\node_modules\typeorm", however no match was resolved for this request (platform = android). Falling back to file-based resolution. Consider updating the call site or asking the package maintainer(s) to expose this API.
 <PERSON><PERSON><PERSON>  Attempted to import the module "E:\App Development\WinArc\node_modules\typeorm" which is listed in the "exports" of "E:\App Development\WinArc\node_modules\typeorm", however no match was resolved for this request (platform = android). Falling back to file-based resolution. Consider updating the call site or asking the package maintainer(s) to expose this API.
Android Bundled 3396ms node_modules\expo-router\entry.js (1780 modules)
 WARN  Require cycle: src\database\entities\Event.ts -> src\database\entities\User.ts -> src\database\entities\Event.ts

Require cycles are allowed, but can result in uninitialized values. Consider refactoring to remove the need for a cycle.
 WARN  Require cycle: src\database\entities\Habit.ts -> src\database\entities\HabitCompletion.ts -> src\database\entities\Habit.ts  

Require cycles are allowed, but can result in uninitialized values. Consider refactoring to remove the need for a cycle.
 WARN  Require cycle: src\database\entities\Habit.ts -> src\database\entities\HabitStreak.ts -> src\database\entities\Habit.ts      

Require cycles are allowed, but can result in uninitialized values. Consider refactoring to remove the need for a cycle.
 WARN  Require cycle: src\database\entities\User.ts -> src\database\entities\Habit.ts -> src\database\entities\User.ts

Require cycles are allowed, but can result in uninitialized values. Consider refactoring to remove the need for a cycle.
 LOG  Database initialized successfully
 ERROR  Failed to initialize DatabaseService: [ReferenceError: Property 'EventRepository' doesn't exist]
 ERROR  Failed to initialize app: [ReferenceError: Property 'EventRepository' doesn't exist]